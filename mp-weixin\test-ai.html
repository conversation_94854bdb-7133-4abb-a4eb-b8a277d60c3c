<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI推荐功能测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .ai-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            font-size: 16px;
            cursor: pointer;
            margin: 10px 0;
            display: block;
            width: 100%;
        }
        .ai-btn:hover {
            opacity: 0.9;
        }
        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.6);
            z-index: 9999;
        }
        .modal.show {
            display: flex;
            align-items: flex-end;
        }
        .modal-content {
            width: 100%;
            max-height: 90%;
            background: white;
            border-radius: 24px 24px 0 0;
            padding: 20px;
            animation: slideUp 0.3s ease-out;
        }
        @keyframes slideUp {
            from { transform: translateY(100%); }
            to { transform: translateY(0); }
        }
        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }
        .close-btn {
            width: 48px;
            height: 48px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            background: #f5f5f5;
            cursor: pointer;
            border: none;
            font-size: 24px;
            color: #666;
            transition: all 0.2s ease;
        }
        .close-btn:hover {
            background: #e0e0e0;
        }
        .close-btn:active {
            background: #d0d0d0;
            transform: scale(0.95);
        }
        .test-result {
            margin: 20px 0;
            padding: 15px;
            border-radius: 8px;
            background: #e8f5e8;
            border: 1px solid #4caf50;
        }
        .test-result.error {
            background: #ffeaea;
            border-color: #f44336;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>AI推荐功能测试</h1>
        <p>这个页面用于测试AI推荐弹窗的关闭功能是否正常工作。</p>
        
        <button class="ai-btn" onclick="openModal()">🤖 打开AI推荐弹窗</button>
        
        <div id="testResult" class="test-result" style="display: none;">
            <strong>测试结果：</strong><span id="resultText"></span>
        </div>
        
        <h2>测试步骤：</h2>
        <ol>
            <li>点击上方的"打开AI推荐弹窗"按钮</li>
            <li>在弹出的弹窗中，点击右上角的关闭按钮（✕）</li>
            <li>观察弹窗是否能正常关闭</li>
        </ol>
        
        <h2>预期结果：</h2>
        <ul>
            <li>弹窗应该能够正常打开</li>
            <li>点击关闭按钮后，弹窗应该立即关闭</li>
            <li>不应该有任何控制台错误</li>
        </ul>
    </div>

    <!-- AI推荐弹窗 -->
    <div id="aiModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <div>
                    <span style="font-size: 24px;">🧠</span>
                    <span style="font-size: 18px; font-weight: bold; margin-left: 10px;">AI智能推荐</span>
                </div>
                <button class="close-btn" onclick="closeModal()">✕</button>
            </div>
            
            <div style="padding: 20px 0;">
                <h3>描述一下你想吃的菜品</h3>
                <div style="margin: 15px 0;">
                    <span style="background: #f0f0f0; padding: 8px 15px; border-radius: 20px; margin: 5px; display: inline-block; cursor: pointer;" onclick="selectExample('我想吃点清淡的菜')">清淡口味</span>
                    <span style="background: #f0f0f0; padding: 8px 15px; border-radius: 20px; margin: 5px; display: inline-block; cursor: pointer;" onclick="selectExample('推荐一些下饭的菜')">下饭菜品</span>
                    <span style="background: #f0f0f0; padding: 8px 15px; border-radius: 20px; margin: 5px; display: inline-block; cursor: pointer;" onclick="selectExample('30元以内的菜')">经济实惠</span>
                </div>
                
                <textarea id="aiInput" style="width: 100%; height: 100px; padding: 15px; border: 1px solid #ddd; border-radius: 8px; resize: none;" placeholder="例如：我想吃点辣的，价格在50元以内，适合两个人吃的菜"></textarea>
                
                <div style="margin-top: 15px; text-align: right;">
                    <span style="color: #999; margin-right: 15px;">0/200</span>
                    <button style="background: #667eea; color: white; border: none; padding: 12px 25px; border-radius: 20px; cursor: pointer;" onclick="getRecommendation()">获取推荐</button>
                </div>
            </div>
        </div>
    </div>

    <script>
        let testStartTime = 0;
        
        function openModal() {
            testStartTime = Date.now();
            document.getElementById('aiModal').classList.add('show');
            showTestResult('弹窗已打开，请点击关闭按钮进行测试', false);
        }
        
        function closeModal() {
            const testDuration = Date.now() - testStartTime;
            document.getElementById('aiModal').classList.remove('show');
            showTestResult(`✅ 关闭功能正常！弹窗在 ${testDuration}ms 内成功关闭`, false);
            
            // 记录测试成功
            console.log('AI推荐弹窗关闭测试通过');
        }
        
        function selectExample(text) {
            document.getElementById('aiInput').value = text;
        }
        
        function getRecommendation() {
            const input = document.getElementById('aiInput').value;
            if (!input.trim()) {
                alert('请输入您的需求');
                return;
            }
            alert('模拟获取推荐中...');
        }
        
        function showTestResult(message, isError) {
            const resultDiv = document.getElementById('testResult');
            const resultText = document.getElementById('resultText');
            
            resultText.textContent = message;
            resultDiv.className = 'test-result' + (isError ? ' error' : '');
            resultDiv.style.display = 'block';
        }
        
        // 点击遮罩关闭弹窗
        document.getElementById('aiModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeModal();
            }
        });
        
        // 阻止弹窗内容区域的点击事件冒泡
        document.querySelector('.modal-content').addEventListener('click', function(e) {
            e.stopPropagation();
        });
    </script>
</body>
</html>
