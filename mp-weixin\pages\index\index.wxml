<view class="data-v-57280228">
  <nav-bar vue-id="8dd740cc-1" class="data-v-57280228" bind:__l="__l"></nav-bar>
  <view data-event-opts="{{[['touchmove',[['disabledScroll',['$event']]]]]}}" class="home_content data-v-57280228" style="{{'padding-top:'+(ht+'px')+';'}}" catchtouchmove="__e">
    <view class="restaurant_info_box data-v-57280228">
      <view class="restaurant_info data-v-57280228">
        <view class="info_top data-v-57280228">
          <view class="info_top_left data-v-57280228">
            <image class="logo_ruiji data-v-57280228" src="../../static/logo_zuijian.png"></image>
          </view>
          <view class="info_top_right data-v-57280228">
            <view class="right_title data-v-57280228"><text class="data-v-57280228">嘴尖秒点派</text>
              <block wx:if="{{shopStatus===1}}">
                <view class="businessStatus data-v-57280228">营业中</view>
              </block>
              <block wx:else>
                <view class="businessStatus close data-v-57280228">休息中</view>
              </block>
            </view>
            <view class="right_details data-v-57280228">
              <view class="details_flex data-v-57280228">
                <image class="top_icon data-v-57280228" src="../../static/length.png"></image><text class="icon_text data-v-57280228">距离1.5km</text>
              </view>
              <view class="details_flex data-v-57280228">
                <image class="top_icon data-v-57280228" src="../../static/money.png"></image><text class="icon_text data-v-57280228">配送费6元</text>
              </view>
              <view class="details_flex test data-v-57280228">
                <image class="top_icon data-v-57280228" src="../../static/time.png"></image><text class="icon_text data-v-57280228">预计时长12min</text>
              </view>
            </view>
          </view>
        </view>
        <view class="info_bottom data-v-57280228">
          <view class="data-v-57280228">
            <view class="word data-v-57280228">为顾客打造专业的大众化美食外送餐饮</view>
            <view class="address data-v-57280228">
              <icon class="data-v-57280228"></icon>四川省德阳市罗江区大学路59号
            </view>
          </view>
          <view class="data-v-57280228">
            <view data-event-opts="{{[['tap',[['handlePhone',['bottom']]]]]}}" class="phone data-v-57280228" bindtap="__e">
              <icon class="phoneIcon data-v-57280228"></icon>
            </view>
          </view>
        </view>
      </view>
    </view>
    <view class="restaurant_menu_list data-v-57280228">
      <view class="type_list data-v-57280228">
        <scroll-view class="u-tab-view menu-scroll-view data-v-57280228" scroll-y="{{true}}" scroll-with-animation="{{true}}" scroll-top="{{scrollTop+100}}" scroll-into-view="{{itemId}}">
          <block wx:for="{{typeListData}}" wx:for-item="item" wx:for-index="index" wx:key="index">
            <view class="{{['type_item','data-v-57280228',typeIndex==index?'active':'']}}" id="target" data-event-opts="{{[['tap',[['swichMenu',['$0',index],[[['typeListData','',index]]]]]]]}}" catchtap="__e">
              <view class="{{['item','data-v-57280228',item.name.length>5?'allLine':'']}}">{{item.name}}</view>
            </view>
          </block>
          <view class="seize_seat data-v-57280228"></view>
        </scroll-view>
      </view>
      <block wx:if="{{dishListItems&&dishListItems.length>0}}">
        <scroll-view class="vegetable_order_list data-v-57280228" scroll-y="true" scroll-top="0rpx">
          <block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index">
            <view class="type_item data-v-57280228">
              <view data-event-opts="{{[['tap',[['openDetailHandle',['$0'],[[['dishListItems','',index]]]]]]]}}" class="dish_img data-v-57280228" bindtap="__e">
                <image class="dish_img_url data-v-57280228" mode="aspectFill" src="{{item.$orig.image}}"></image>
              </view>
              <view class="dish_info data-v-57280228">
                <view data-event-opts="{{[['tap',[['openDetailHandle',['$0'],[[['dishListItems','',index]]]]]]]}}" class="dish_name data-v-57280228" bindtap="__e">{{item.$orig.name}}</view>
                <view data-event-opts="{{[['tap',[['openDetailHandle',['$0'],[[['dishListItems','',index]]]]]]]}}" class="dish_label data-v-57280228" bindtap="__e">{{item.$orig.description||item.$orig.name}}</view>
                <view data-event-opts="{{[['tap',[['openDetailHandle',['$0'],[[['dishListItems','',index]]]]]]]}}" class="dish_label data-v-57280228" bindtap="__e">月销量0</view>
                <view class="dish_price data-v-57280228"><text class="ico data-v-57280228">￥</text>{{''+item.g0+''}}</view>
                <block wx:if="{{!item.$orig.flavors||item.$orig.flavors.length===0}}">
                  <view class="dish_active data-v-57280228">
                    <block wx:if="{{item.$orig.dishNumber>=1}}">
                      <image class="dish_red data-v-57280228" src="../../static/btn_red.png" data-event-opts="{{[['tap',[['redDishAction',['$0','普通'],[[['dishListItems','',index]]]]]]]}}" bindtap="__e"></image>
                    </block>
                    <block wx:if="{{item.$orig.dishNumber>0}}"><text class="dish_number data-v-57280228">{{item.$orig.dishNumber}}</text></block>
                    <image class="dish_add data-v-57280228" src="../../static/btn_add.png" data-event-opts="{{[['tap',[['addDishAction',['$0','普通'],[[['dishListItems','',index]]]]]]]}}" bindtap="__e"></image>
                  </view>
                </block>
                <block wx:else>
                  <view class="dish_active_btn data-v-57280228">
                    <view data-event-opts="{{[['tap',[['moreNormDataesHandle',['$0'],[[['dishListItems','',index]]]]]]]}}" class="check_but data-v-57280228" bindtap="__e">选择规格</view>
                  </view>
                </block>
              </view>
            </view>
          </block>
          <view class="seize_seat data-v-57280228"></view>
        </scroll-view>
      </block>
      <block wx:else>
        <view class="no_dish data-v-57280228">
          <block wx:if="{{typeListData.length>0}}">
            <view class="data-v-57280228">该分类下暂无菜品</view>
          </block>
        </view>
      </block>
    </view>
    <view class="mask-box data-v-57280228"></view>
    <block wx:if="{{$root.m0.length===0}}">
      <view class="footer_order_buttom data-v-57280228">
        <view class="order_number data-v-57280228">
          <image class="order_number_icon data-v-57280228" src="../../static/btn_waiter_nor.png" mode></image>
        </view>
        <view class="order_price data-v-57280228"><text class="ico data-v-57280228">￥</text>0</view>
        <view class="ord<strong></strong>er_but data-v-57280228">去结算</view>
      </view>
    </block>
    <block wx:else>
      <view class="footer_order_buttom order_form data-v-57280228">
        <view data-event-opts="{{[['tap',[['e0',['$event']]]]]}}" class="orderCar data-v-57280228" bindtap="__e">
          <view class="order_number data-v-57280228">
            <image class="order_number_icon data-v-57280228" src="../../static/btn_waiter_sel.png" mode></image>
            <view class="order_dish_num data-v-57280228">{{orderDishNumber}}</view>
          </view>
          <view class="order_price data-v-57280228"><text class="ico data-v-57280228">￥</text>{{''+$root.g1+''}}</view>
        </view>
        <view data-event-opts="{{[['tap',[['goOrder']]]]}}" class="order_but data-v-57280228" bindtap="__e">去结算</view>
      </view>
    </block>
    <view hidden="{{!(openMoreNormPop)}}" class="pop_mask  data-v-57280228">
      <view class="more_norm_pop data-v-57280228">
        <view class="title data-v-57280228">{{moreNormDishdata.name}}</view>
        <scroll-view class="items_cont data-v-57280228" scroll-y="true" scroll-top="0rpx">
          <block wx:for="{{$root.l2}}" wx:for-item="obj" wx:for-index="index" wx:key="index">
            <view class="item_row data-v-57280228">
              <view class="flavor_name data-v-57280228">{{obj.$orig.name}}</view>
              <view class="flavor_item data-v-57280228">
                <block wx:for="{{obj.l1}}" wx:for-item="item" wx:for-index="ind" wx:key="ind">
                  <view data-event-opts="{{[['tap',[['checkMoreNormPop',['$0','$1'],[[['moreNormdata','',index,'value']],[['moreNormdata','',index],['value','',ind]]]]]]]}}" class="{{['data-v-57280228',(true)?'item':'',(item.g2!==-1)?'act':'']}}" bindtap="__e">{{''+item.$orig+''}}</view>
                </block>
              </view>
            </view>
          </block>
        </scroll-view>
        <view class="but_item data-v-57280228">
          <view class="price data-v-57280228"><text class="ico data-v-57280228">￥</text>{{''+moreNormDishdata.price+''}}</view>
          <view class="active data-v-57280228">
            <view data-event-opts="{{[['tap',[['addShop',['$0','普通'],['moreNormDishdata']]]]]}}" class="dish_card_add data-v-57280228" bindtap="__e">加入购物车</view>
          </view>
        </view>
        <view data-event-opts="{{[['tap',[['closeMoreNorm',['$0'],['moreNormDishdata']]]]]}}" class="close data-v-57280228" bindtap="__e">
          <image class="close_img data-v-57280228" src="../../static/but_close.png" mode></image>
        </view>
      </view>
    </view>
    <view hidden="{{!(openDetailPop)}}" class="pop_mask  data-v-57280228" style="z-index:9999;">
      <block wx:if="{{dishDetailes.type==1}}">
        <view class="dish_detail_pop data-v-57280228">
          <image class="div_big_image data-v-57280228" mode="aspectFill" src="{{dishDetailes.image}}"></image>
          <view class="title data-v-57280228">{{dishDetailes.name}}</view>
          <view class="desc data-v-57280228">{{dishDetailes.description}}</view>
          <view class="but_item data-v-57280228">
            <view class="price data-v-57280228"><text class="ico data-v-57280228">￥</text>{{''+$root.g3+''}}</view>
            <block wx:if="{{dishDetailes.flavors.length===0&&dishDetailes.dishNumber>0}}">
              <view class="active data-v-57280228">
                <image class="dish_red data-v-57280228" src="../../static/btn_red.png" mode data-event-opts="{{[['tap',[['redDishAction',['$0','普通'],['dishDetailes']]]]]}}" bindtap="__e"></image><text class="dish_number data-v-57280228">{{dishDetailes.dishNumber}}</text>
                <image class="dish_add data-v-57280228" src="../../static/btn_add.png" mode data-event-opts="{{[['tap',[['addDishAction',['$0','普通'],['dishDetailes']]]]]}}" bindtap="__e"></image>
              </view>
            </block>
            <block wx:if="{{dishDetailes.flavors.length>0}}">
              <view class="active data-v-57280228">
                <view data-event-opts="{{[['tap',[['moreNormDataesHandle',['$0'],['dishDetailes']]]]]}}" class="dish_card_add data-v-57280228" bindtap="__e">选择规格</view>
              </view>
            </block>
            <block wx:if="{{dishDetailes.dishNumber===0&&dishDetailes.flavors.length===0}}">
              <view class="active data-v-57280228">
                <view data-event-opts="{{[['tap',[['addDishAction',['$0','普通'],['dishDetailes']]]]]}}" class="dish_card_add data-v-57280228" bindtap="__e">加入购物车</view>
              </view>
            </block>
          </view>
          <view data-event-opts="{{[['tap',[['e1',['$event']]]]]}}" class="close data-v-57280228" bindtap="__e">
            <image class="close_img data-v-57280228" src="../../static/but_close.png" mode></image>
          </view>
        </view>
      </block>
      <block wx:else>
        <view class="dish_detail_pop data-v-57280228">
          <scroll-view class="dish_items data-v-57280228" scroll-y="true" scroll-top="0rpx">
            <block wx:for="{{dishMealData}}" wx:for-item="item" wx:for-index="index" wx:key="index">
              <view class="dish_item data-v-57280228">
                <image class="div_big_image data-v-57280228" src="{{item.image}}" mode></image>
                <view class="title data-v-57280228">{{''+item.name+''}}<text class="data-v-57280228">{{"X"+item.copies}}</text></view>
                <view class="desc data-v-57280228">{{item.description}}</view>
              </view>
            </block>
          </scroll-view>
          <view class="but_item data-v-57280228">
            <view class="price data-v-57280228"><text class="ico data-v-57280228">￥</text>{{''+dishDetailes.price+''}}</view>
            <block wx:if="{{dishDetailes.dishNumber&&dishDetailes.dishNumber>0}}">
              <view class="active data-v-57280228">
                <image class="dish_red data-v-57280228" src="../../static/btn_red.png" mode data-event-opts="{{[['tap',[['redDishAction',['$0','普通'],['dishDetailes']]]]]}}" bindtap="__e"></image><text class="dish_number data-v-57280228">{{dishDetailes.dishNumber}}</text>
                <image class="dish_add data-v-57280228" src="../../static/btn_add.png" mode data-event-opts="{{[['tap',[['addDishAction',['$0','普通'],['dishDetailes']]]]]}}" bindtap="__e"></image>
              </view>
            </block>
            <block wx:else>
              <block wx:if="{{dishDetailes.dishNumber==0}}">
                <view class="active data-v-57280228">
                  <view data-event-opts="{{[['tap',[['addDishAction',['$0','普通'],['dishDetailes']]]]]}}" class="dish_card_add data-v-57280228" bindtap="__e">加入购物车</view>
                </view>
              </block>
            </block>
          </view>
          <view data-event-opts="{{[['tap',[['e2',['$event']]]]]}}" class="close data-v-57280228" bindtap="__e">
            <image class="close_img data-v-57280228" src="../../static/but_close.png" mode></image>
          </view>
        </view>
      </block>
    </view>
    <view data-event-opts="{{[['tap',[['e3',['$event']]]]]}}" hidden="{{!(openOrderCartList)}}" class="pop_mask  data-v-57280228" bindtap="__e">
      <view data-event-opts="{{[['tap',[['e4',['$event']]]]]}}" class="cart_pop data-v-57280228" catchtap="__e">
        <view class="top_title data-v-57280228">
          <view class="tit data-v-57280228">购物车</view>
          <view data-event-opts="{{[['tap',[['clearCardOrder']]]]}}" class="clear data-v-57280228" catchtap="__e">
            <image class="clear_icon data-v-57280228" src="../../static/clear.png" mode></image><text class="clear-des data-v-57280228">清空</text>
          </view>
        </view>
        <scroll-view class="card_order_list data-v-57280228" scroll-y="true" scroll-top="40rpx">
          <block wx:for="{{orderAndUserInfo}}" wx:for-item="item" wx:for-index="ind" wx:key="ind">
            <view class="type_item_cont data-v-57280228">
              <block wx:for="{{item.dishList}}" wx:for-item="obj" wx:for-index="index" wx:key="index">
                <view class="type_item data-v-57280228">
                  <view class="dish_img data-v-57280228">
                    <image class="dish_img_url data-v-57280228" mode="aspectFill" src="{{obj.image}}"></image>
                  </view>
                  <view class="dish_info data-v-57280228">
                    <view class="dish_name data-v-57280228">{{obj.name}}</view>
                    <block wx:if="{{obj.dishFlavor}}">
                      <view class="dish_dishFlavor data-v-57280228">{{obj.dishFlavor}}</view>
                    </block>
                    <view class="dish_price data-v-57280228"><text class="ico data-v-57280228">￥</text>{{''+obj.amount+''}}</view>
                    <view class="dish_active data-v-57280228">
                      <block wx:if="{{obj.number&&obj.number>0}}">
                        <image class="dish_red data-v-57280228" src="../../static/btn_red.png" mode data-event-opts="{{[['tap',[['redDishAction',['$0','购物车'],[[['orderAndUserInfo','',ind],['dishList','',index]]]]]]]}}" catchtap="__e"></image>
                      </block>
                      <block wx:if="{{obj.number&&obj.number>0}}"><text class="dish_number data-v-57280228">{{obj.number}}</text></block>
                      <image class="dish_add data-v-57280228" src="../../static/btn_add.png" mode data-event-opts="{{[['tap',[['addDishAction',['$0','购物车'],[[['orderAndUserInfo','',ind],['dishList','',index]]]]]]]}}" catchtap="__e"></image>
                    </view>
                  </view>
                </view>
              </block>
            </view>
          </block>
          <view class="seize_seat data-v-57280228"></view>
        </scroll-view>
      </view>
    </view>
    <view hidden="{{!(loaddingSt)}}" class="pop_mask data-v-57280228">
      <view class="lodding data-v-57280228">
        <image class="lodding_ico data-v-57280228" src="../../static/lodding.gif" mode></image>
      </view>
    </view>
    <phone vue-id="8dd740cc-2" phoneData="{{phoneData}}" data-ref="phone" data-event-opts="{{[['^closePopup',[['closePopup']]]]}}" bind:closePopup="__e" class="data-v-57280228 vue-ref" bind:__l="__l"></phone>
    <block wx:if="{{shopStatus===0}}">
      <view class="colseShop data-v-57280228">
        <view class="shop data-v-57280228">本店已打样</view>
      </view>
    </block>
  </view>
</view>