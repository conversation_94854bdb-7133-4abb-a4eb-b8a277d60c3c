package com.sky.service.impl;


import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.sky.constant.MessageConstant;
import com.sky.dto.DishDTO;
import com.sky.dto.DishPageQueryDTO;
import com.sky.entity.Dish;
import com.sky.entity.DishFlavor;
import com.sky.exception.DeletionNotAllowedException;
import com.sky.mapper.DishFlavorMapper;
import com.sky.mapper.DishMapper;
import com.sky.mapper.SetmealMapper;
import com.sky.result.PageResult;
import com.sky.service.DishService;
import com.sky.vo.DishVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

@Slf4j
@Service
public class DishServiceImpl implements DishService {
    @Autowired
    private DishMapper dishMapper;
    @Autowired
    private DishFlavorMapper dishFlavorMapper;
    @Autowired
    private SetmealMapper setmealMapper;
    /**
     * 新增菜品
     *
     * @param dishDTO
     */
    @Override
    public void addDish(DishDTO dishDTO) {
        //因为数据表里面插入的数据是dish实体类，所以要先创建一个dish对象
        Dish dish = new Dish();
        //将属性还要全部奉还给dish进行插入操作
        BeanUtils.copyProperties(dishDTO, dish);
        //暂无补充的属性，采用了AutoFill自动填充了常见的数据
        //开始插入数据
        dishMapper.insert(dish);
        //得到dish的id编号
        Long id = dish.getId();
        //将口味拿出来准备传给dishFlavor数据表
        List<DishFlavor> flavors = dishDTO.getFlavors();
        //因为flavors中没有dishId的数据，故要迭代设置一下
        //但是要注意，如果flavors中没有数据，那么就要跳过这一步
        if (flavors != null && flavors.size() != 0) {
            log.info("当前菜品口味如下：{}", flavors);
            flavors.forEach(flavor -> {
                flavor.setDishId(id);
            });
            //插入多条数据
            dishFlavorMapper.insert(flavors);
        }
    }

    /**
     * 菜品分页查询
     *
     * @param dto
     * @return
     */
    @Override
    public PageResult list(DishPageQueryDTO dto) {
        //设置分页参数
        PageHelper.startPage(dto.getPage(), dto.getPageSize());
        //调用mapper方法查询后强转为Page类型
        Page<DishVO> page = dishMapper.list(dto);
        //返回总记录数和集合
        return new PageResult(page.getTotal(), page.getResult());
    }

    /**
     * 删除菜品
     *
     * @param ids
     */
    @Transactional//忘记加事务注解了
    @Override
    public void deleteDish(List<Long> ids) {
        //先判断起售的菜品不能删除！ids可能是一个数组，所以要循环遍历
        ids.forEach(id -> {
            Integer status = dishMapper.selectByDishId(id);
            if (status == 1) {
                //如果处于起售状态，则无法删除并抛出异常
                throw new DeletionNotAllowedException(MessageConstant.DISH_ON_SALE);
            }
        });
        //判断被套餐关联的菜品不能删除，有可能会返回多个关联id，所以要用列表接收
        ids.forEach(id -> {
            List<Long> setmealIds = setmealMapper.selectSetmealIdByDishId(id);
            //看一下菜品是否被套餐关联了
            if (setmealIds.size() > 0 && setmealIds != null) {
                setmealIds.clear();//清除一下内容
                throw new DeletionNotAllowedException(MessageConstant.DISH_BE_RELATED_BY_SETMEAL);
            }
        });
        //检查以上都没有问题，可以放心删除菜品和关联的口味数据
        //删除菜品
        dishMapper.deleteDishById(ids);
        //删除关联口味
        dishFlavorMapper.deleteDishFlavorById(ids);


    }

    /**
     * 根据id查询菜品
     *
     * @param id
     * @return
     */
    @Override
    public DishVO getDishById(Long id) {
        //先查找菜品
        Dish dish = dishMapper.getByDishId(id);
        //再查找口味(一个菜品可能会有多个口味，所以应该用List数组进行存储)
        List<DishFlavor> dishFlavors = dishFlavorMapper.getFlavorsByDishId(id);
        //组合数据，使用VO视图传递给前端
        DishVO dishVO = new DishVO();
        //使用BeanUtils直接copy属性过来
        BeanUtils.copyProperties(dish, dishVO);
        //还剩下一个口味数据还没有设置
        dishVO.setFlavors(dishFlavors);
        return dishVO;
    }

    /**
     * 修改菜品
     *
     * @param dto
     */
    @Transactional//启动事务管理
    @Override
    public void updateDish(DishDTO dto) {
        //要更新dish表，就要封装实体对象进行更新
        Dish dish = new Dish();
        //赋值传递的属性给dish
        BeanUtils.copyProperties(dto,dish);
        //在mapper层使用@AutoFill进行补充剩余属性
        dishMapper.updateDish(dish);
        //先删除口味，设计失误，先新建一个菜品id数组
        List<Long> ids=new ArrayList<>();
        ids.add(dto.getId());
        //虽然只有一个id但只能这样了
        dishFlavorMapper.deleteDishFlavorById(ids);
        //再添加口味
        //先要设置dishId，因为前端没有传递
        List<DishFlavor> dishFlavors=dto.getFlavors();
        dishFlavors.forEach(dishFlavor -> {
            dishFlavor.setDishId(dto.getId());
        });
        dishFlavorMapper.insert(dishFlavors);
    }

    /**
     * 根据分类id查询菜品
     *
     * @param categoryId
     * @return
     */
    @Override
    public List<DishVO> getDishByCategoryId(Long categoryId) {
        List<DishVO> dishVO=dishMapper.selectDishByCategoryId(categoryId);
        return dishVO;
    }

    /**
     * 菜品起售、停售服务
     * @param status
     * @param id
     */
    @Transactional
    @Override
    public void enableOrDisable(Integer status, Long id) {
        //a. 菜品停售，则包含菜品的套餐同时停售。
        dishMapper.updateStatus(status,id);
        if(status==0){//只能够满足同时停售，不能同时起售
            setmealMapper.updateStatus(status,dishMapper.selectSetmealIdByDishId(id));
        }
    }

    /**
     * 条件查询菜品和口味
     * @param dish
     * @return
     */
    public List<DishVO> listWithFlavor(Dish dish) {
        List<Dish> dishList = dishMapper.listOfDish(dish);

        List<DishVO> dishVOList = new ArrayList<>();

        for (Dish d : dishList) {
            DishVO dishVO = new DishVO();
            BeanUtils.copyProperties(d,dishVO);

            //根据菜品id查询对应的口味
            List<DishFlavor> flavors = dishFlavorMapper.getFlavorsByDishId(d.getId());

            dishVO.setFlavors(flavors);
            dishVOList.add(dishVO);
        }

        return dishVOList;
    }
}
