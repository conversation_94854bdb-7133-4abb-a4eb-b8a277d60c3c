/**
 * AI智能推荐工具类
 */

// 配置信息
const AI_CONFIG = {
  baseUrl: 'http://localhost:8080',
  timeout: 30000
};

/**
 * AI推荐数据管理
 */
class AIRecommendationManager {
  constructor() {
    this.data = {
      showAIRecommendation: false,
      aiInputText: '',
      aiLoading: false,
      aiRecommendations: [],
      aiSummary: '',
      hasSearched: false
    };
  }

  /**
   * 获取当前数据
   */
  getData() {
    return this.data;
  }

  /**
   * 更新数据
   */
  updateData(newData) {
    Object.assign(this.data, newData);
  }

  /**
   * 重置数据
   */
  resetData() {
    this.data = {
      showAIRecommendation: false,
      aiInputText: '',
      aiLoading: false,
      aiRecommendations: [],
      aiSummary: '',
      hasSearched: false
    };
  }

  /**
   * 打开AI推荐弹窗
   */
  openAIRecommendation() {
    this.updateData({
      showAIRecommendation: true,
      aiInputText: '',
      aiRecommendations: [],
      aiSummary: '',
      hasSearched: false
    });
  }

  /**
   * 关闭AI推荐弹窗
   */
  closeAIRecommendation() {
    this.updateData({
      showAIRecommendation: false
    });
  }

  /**
   * 选择示例文本
   */
  selectExample(text) {
    this.updateData({
      aiInputText: text
    });
  }

  /**
   * 输入文本变化
   */
  onAIInputChange(value) {
    this.updateData({
      aiInputText: value
    });
  }

  /**
   * 发送AI推荐请求
   */
  async requestAIRecommendation() {
    return new Promise((resolve, reject) => {
      wx.request({
        url: `${AI_CONFIG.baseUrl}/user/recommendation/dishes`,
        method: 'POST',
        header: {
          'Content-Type': 'application/json',
          'authentication': wx.getStorageSync('token') || ''
        },
        data: {
          userRequirement: this.data.aiInputText.trim(),
          maxResults: 5
        },
        timeout: AI_CONFIG.timeout,
        success: (res) => {
          console.log('AI推荐响应:', res.data);
          resolve(res.data);
        },
        fail: (error) => {
          console.error('AI推荐请求失败:', error);
          reject(error);
        }
      });
    });
  }

  /**
   * 获取AI推荐
   */
  async getAIRecommendation() {
    if (!this.data.aiInputText.trim()) {
      wx.showToast({
        title: '请输入您的需求',
        icon: 'none'
      });
      return false;
    }

    this.updateData({
      aiLoading: true,
      hasSearched: true
    });

    try {
      const response = await this.requestAIRecommendation();
      
      if (response.code === 1) {
        this.updateData({
          aiRecommendations: response.data.recommendations || [],
          aiSummary: response.data.aiSummary || ''
        });

        if (response.data.recommendations.length === 0) {
          wx.showToast({
            title: '暂无匹配的推荐',
            icon: 'none'
          });
        }
        return true;
      } else {
        wx.showToast({
          title: response.msg || '推荐失败，请重试',
          icon: 'none'
        });
        return false;
      }
    } catch (error) {
      console.error('AI推荐请求失败:', error);
      wx.showToast({
        title: '网络错误，请重试',
        icon: 'none'
      });
      return false;
    } finally {
      this.updateData({
        aiLoading: false
      });
    }
  }

  /**
   * 显示推荐菜品详情
   */
  viewRecommendedDish(dish, callback) {
    if (typeof callback === 'function') {
      callback(dish);
    }
    this.closeAIRecommendation();
  }

  /**
   * 添加推荐菜品到购物车
   */
  addRecommendedToCart(dish, callback) {
    if (typeof callback === 'function') {
      callback(dish, 'AI推荐');
    }
    
    wx.showToast({
      title: '已添加到购物车',
      icon: 'success'
    });
  }
}

/**
 * AI推荐混入对象
 * 可以在页面中使用 Object.assign(this, aiRecommendationMixin) 来混入功能
 */
const aiRecommendationMixin = {
  // AI推荐管理器实例
  aiManager: new AIRecommendationManager(),

  /**
   * 打开AI推荐弹窗
   */
  openAIRecommendation() {
    console.log('混入对象的openAIRecommendation方法被调用');
    console.log('当前showAIRecommendation状态:', this.data.showAIRecommendation);

    this.aiManager.openAIRecommendation();
    const newData = this.aiManager.getData();
    console.log('准备设置的新数据:', newData);

    this.setData(newData);

    console.log('设置数据后的showAIRecommendation状态:', this.data.showAIRecommendation);
  },

  /**
   * 关闭AI推荐弹窗
   */
  closeAIRecommendation() {
    console.log('混入对象的closeAIRecommendation方法被调用');
    console.log('当前showAIRecommendation状态:', this.data.showAIRecommendation);

    this.aiManager.closeAIRecommendation();
    const newData = this.aiManager.getData();
    console.log('准备设置的新数据:', newData);

    this.setData(newData);

    console.log('设置数据后的showAIRecommendation状态:', this.data.showAIRecommendation);
  },

  /**
   * 选择示例文本
   */
  selectExample(e) {
    const text = e.currentTarget.dataset.text;
    this.aiManager.selectExample(text);
    this.setData(this.aiManager.getData());
  },

  /**
   * 输入文本变化
   */
  onAIInputChange(e) {
    this.aiManager.onAIInputChange(e.detail.value);
    this.setData(this.aiManager.getData());
  },

  /**
   * 获取AI推荐
   */
  async getAIRecommendation() {
    const success = await this.aiManager.getAIRecommendation();
    this.setData(this.aiManager.getData());
    return success;
  },

  /**
   * 查看推荐菜品详情
   */
  viewRecommendedDish(e) {
    const dish = e.currentTarget.dataset.dish;
    this.aiManager.viewRecommendedDish(dish, (dishData) => {
      // 调用现有的菜品详情显示方法
      if (typeof this.openDetailHandle === 'function') {
        this.openDetailHandle(dishData);
      }
    });
    this.setData(this.aiManager.getData());
  },

  /**
   * 添加推荐菜品到购物车
   */
  addRecommendedToCart(e) {
    const dish = e.currentTarget.dataset.dish;
    this.aiManager.addRecommendedToCart(dish, (dishData, source) => {
      // 调用现有的添加购物车方法
      if (typeof this.addDishAction === 'function') {
        this.addDishAction(dishData, source);
      }
    });
    this.setData(this.aiManager.getData());
  }
};

// 导出
module.exports = {
  AIRecommendationManager,
  aiRecommendationMixin,
  AI_CONFIG
};
